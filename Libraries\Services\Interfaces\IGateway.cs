#nullable enable
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.Email;
using Models.DTOs.SMS;
using Models.DTOs.MessengerApp;
using Models.DTOs.Push;
using Models.DTOs.Gateway;

namespace Services.Interfaces;

/// <summary>
/// Base interface for all messaging gateways
/// </summary>
public interface IGateway
{
    /// <summary>
    /// Gateway provider name
    /// </summary>
    string ProviderName { get; }

    /// <summary>
    /// Whether the gateway is currently enabled
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// Initialize the gateway with configuration
    /// </summary>
    Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test the gateway connection and configuration
    /// </summary>
    Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get gateway capabilities and features
    /// </summary>
    GatewayCapabilities GetCapabilities();
}

/// <summary>
/// Generic gateway interface for sending messages
/// </summary>
public interface IMessageGateway<TPayload, TResult> : IGateway
{
    /// <summary>
    /// Send a single message
    /// </summary>
    Task<TResult> SendAsync(TPayload payload, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send multiple messages in bulk
    /// </summary>
    Task<IReadOnlyList<TResult>> SendBulkAsync(IEnumerable<TPayload> payloads, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get message status
    /// </summary>
    Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for gateways that support message scheduling
/// </summary>
public interface ISchedulableGateway<TPayload, TScheduleResult> : IGateway
{
    /// <summary>
    /// Schedule a message for future delivery
    /// </summary>
    Task<TScheduleResult> ScheduleMessageAsync(TPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel a scheduled message
    /// </summary>
    Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update a scheduled message
    /// </summary>
    Task<TScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, TPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Gateway health check result
/// </summary>
public class GatewayHealthResult
{
    public bool IsHealthy { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object>? AdditionalInfo { get; set; }
}

/// <summary>
/// Gateway capabilities and features
/// </summary>
public class GatewayCapabilities
{
    public bool SupportsBulkSending { get; set; }
    public bool SupportsScheduling { get; set; }
    public bool SupportsDeliveryReceipts { get; set; }
    public bool SupportsReadReceipts { get; set; }
    public bool SupportsTemplates { get; set; }
    public bool SupportsAttachments { get; set; }
    public bool SupportsRichContent { get; set; }
    public int MaxMessageSize { get; set; }
    public int MaxBulkSize { get; set; }
    public int RateLimitPerMinute { get; set; }
    public List<string> SupportedContentTypes { get; set; } = new();
    public List<string> SupportedFeatures { get; set; } = new();
}

/// <summary>
/// Message status information
/// </summary>
public class MessageStatus
{
    public string MessageId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // sent, delivered, read, failed, etc.
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, object>? AdditionalInfo { get; set; }
}

/// <summary>
/// Gateway factory interface for creating gateway instances
/// </summary>
public interface IGatewayFactory<TPayload, TResult>
{
    /// <summary>
    /// Create a gateway instance for the specified provider
    /// </summary>
    IMessageGateway<TPayload, TResult> CreateGateway(string providerName);

    /// <summary>
    /// Get all available provider names
    /// </summary>
    IReadOnlyList<string> GetAvailableProviders();

    /// <summary>
    /// Check if a provider is supported
    /// </summary>
    bool IsProviderSupported(string providerName);
}

/// <summary>
/// Schedulable gateway factory interface
/// </summary>
public interface ISchedulableGatewayFactory<TPayload, TResult, TScheduleResult> : IGatewayFactory<TPayload, TResult>
{
    /// <summary>
    /// Create a schedulable gateway instance for the specified provider
    /// </summary>
    ISchedulableGateway<TPayload, TScheduleResult> CreateSchedulableGateway(string providerName);
}

/// <summary>
/// Administrative operations interface for gateways
/// </summary>
public interface IAdminGateway
{
    /// <summary>
    /// Get current gateway configuration (sanitized - no secrets)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current configuration</returns>
    Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Update gateway configuration
    /// </summary>
    /// <param name="configuration">New configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration update result</returns>
    Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available templates for this gateway
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Available templates</returns>
    Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update a template
    /// </summary>
    /// <param name="template">Template definition</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template operation result</returns>
    Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a template
    /// </summary>
    /// <param name="templateId">Template ID to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template operation result</returns>
    Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get retry configuration for this gateway
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Retry configuration</returns>
    Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Update retry configuration
    /// </summary>
    /// <param name="retryConfig">New retry configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Configuration update result</returns>
    Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test gateway with sample message
    /// </summary>
    /// <param name="testPayload">Test message payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test result</returns>
    Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default);
}

/// <summary>
/// Metrics and analytics interface for gateways
/// </summary>
public interface IMetricsGateway
{
    /// <summary>
    /// Get usage statistics for the gateway
    /// </summary>
    /// <param name="startDate">Start date for metrics</param>
    /// <param name="endDate">End date for metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Usage metrics</returns>
    Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get success rate metrics
    /// </summary>
    /// <param name="startDate">Start date for metrics</param>
    /// <param name="endDate">End date for metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success rate metrics</returns>
    Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get failure rate and error analysis
    /// </summary>
    /// <param name="startDate">Start date for metrics</param>
    /// <param name="endDate">End date for metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Failure rate metrics</returns>
    Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get latency and performance metrics
    /// </summary>
    /// <param name="startDate">Start date for metrics</param>
    /// <param name="endDate">End date for metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Latency metrics</returns>
    Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get delivery count metrics
    /// </summary>
    /// <param name="startDate">Start date for metrics</param>
    /// <param name="endDate">End date for metrics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery count metrics</returns>
    Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get comprehensive analytics dashboard data
    /// </summary>
    /// <param name="startDate">Start date for analytics</param>
    /// <param name="endDate">End date for analytics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Analytics dashboard data</returns>
    Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get real-time metrics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Real-time metrics</returns>
    Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default);
}

// ===== ADMIN GATEWAY MODELS =====

/// <summary>
/// Gateway test result
/// </summary>
public class GatewayTestResult:BaseGateway
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public string TestMessageId { get; set; } = string.Empty;
    public Dictionary<string, object> TestDetails { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime TestedAt { get; set; } = DateTime.UtcNow;
}

// ===== METRICS GATEWAY MODELS =====

/// <summary>
/// Gateway usage metrics
/// </summary>
public class GatewayUsageMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public long TotalMessages { get; set; }
    public long TotalBulkOperations { get; set; }
    public long TotalScheduledMessages { get; set; }
    public Dictionary<string, long> MessagesByDay { get; set; } = new();
    public Dictionary<string, long> MessagesByHour { get; set; } = new();
    public Dictionary<string, long> MessagesByType { get; set; } = new();
    public double AverageMessagesPerDay { get; set; }
    public double PeakMessagesPerHour { get; set; }
}

/// <summary>
/// Gateway success rate metrics
/// </summary>
public class GatewaySuccessRateMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public long TotalAttempts { get; set; }
    public long SuccessfulDeliveries { get; set; }
    public double SuccessRate { get; set; }
    public Dictionary<string, double> SuccessRateByDay { get; set; } = new();
    public Dictionary<string, double> SuccessRateByHour { get; set; } = new();
    public Dictionary<string, long> SuccessfulByType { get; set; } = new();
    public double TrendDirection { get; set; } // Positive = improving, Negative = declining
}

/// <summary>
/// Gateway failure rate metrics
/// </summary>
public class GatewayFailureRateMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public long TotalFailures { get; set; }
    public double FailureRate { get; set; }
    public Dictionary<string, long> FailuresByErrorCode { get; set; } = new();
    public Dictionary<string, long> FailuresByDay { get; set; } = new();
    public Dictionary<string, string> TopErrorMessages { get; set; } = new();
    public string[] MostCommonErrors { get; set; } = Array.Empty<string>();
    public Dictionary<string, double> FailureRateByType { get; set; } = new();
}

/// <summary>
/// Gateway latency metrics
/// </summary>
public class GatewayLatencyMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public double AverageLatencyMs { get; set; }
    public double MedianLatencyMs { get; set; }
    public double P95LatencyMs { get; set; }
    public double P99LatencyMs { get; set; }
    public double MinLatencyMs { get; set; }
    public double MaxLatencyMs { get; set; }
    public Dictionary<string, double> LatencyByDay { get; set; } = new();
    public Dictionary<string, double> LatencyByHour { get; set; } = new();
    public Dictionary<string, double> LatencyByType { get; set; } = new();
}

/// <summary>
/// Gateway delivery count metrics
/// </summary>
public class GatewayDeliveryCountMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public long TotalSent { get; set; }
    public long TotalDelivered { get; set; }
    public long TotalRead { get; set; }
    public long TotalBounced { get; set; }
    public long TotalComplained { get; set; }
    public Dictionary<string, long> DeliveryByDay { get; set; } = new();
    public Dictionary<string, long> ReadByDay { get; set; } = new();
    public Dictionary<string, long> BounceByDay { get; set; } = new();
    public double DeliveryRate { get; set; }
    public double ReadRate { get; set; }
    public double BounceRate { get; set; }
}

/// <summary>
/// Gateway analytics dashboard data
/// </summary>
public class GatewayAnalyticsDashboard
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public GatewayUsageMetrics Usage { get; set; } = new();
    public GatewaySuccessRateMetrics SuccessRate { get; set; } = new();
    public GatewayFailureRateMetrics FailureRate { get; set; } = new();
    public GatewayLatencyMetrics Latency { get; set; } = new();
    public GatewayDeliveryCountMetrics DeliveryCount { get; set; } = new();
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
    public string[] Insights { get; set; } = Array.Empty<string>();
    public string[] Recommendations { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Gateway real-time metrics
/// </summary>
public class GatewayRealTimeMetrics
{
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public long MessagesInLast5Minutes { get; set; }
    public long MessagesInLastHour { get; set; }
    public long MessagesInLastDay { get; set; }
    public double CurrentSuccessRate { get; set; }
    public double CurrentLatencyMs { get; set; }
    public long ActiveConnections { get; set; }
    public long QueuedMessages { get; set; }
    public string HealthStatus { get; set; } = "healthy";
    public Dictionary<string, object> LiveStats { get; set; } = new();
    public string[] ActiveAlerts { get; set; } = Array.Empty<string>();
}

// Specific Gateway Interfaces
/// <summary>
/// Email gateway interface
/// </summary>
public interface IEmailGateway : IMessageGateway<EmailPayload, EmailResult>, ISchedulableGateway<EmailPayload, EmailScheduleResult>
{
    /// <summary>
    /// Send email with template
    /// </summary>
    Task<EmailResult> SendWithTemplateAsync(string templateId, object templateData, string recipient, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk emails with template
    /// </summary>
    Task<IReadOnlyList<EmailResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipient)> recipients, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate email address
    /// </summary>
    Task<EmailValidationResult> ValidateEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get delivery report for email
    /// </summary>
    Task<EmailDeliveryReport> GetDeliveryReportAsync(string messageId, CancellationToken cancellationToken = default);
}

/// <summary>
/// SMS gateway interface
/// </summary>
public interface ISmsGateway : IMessageGateway<SmsPayload, SmsResult>, ISchedulableGateway<SmsPayload, SmsScheduleResult>
{
    /// <summary>
    /// Send SMS with template
    /// </summary>
    Task<SmsResult> SendWithTemplateAsync(string templateId, object templateData, string recipient, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk SMS with template
    /// </summary>
    Task<IReadOnlyList<SmsResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipient)> recipients, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate phone number
    /// </summary>
    Task<PhoneValidationResult> ValidatePhoneAsync(string phoneNumber, CancellationToken cancellationToken = default);
}

/// <summary>
/// MessengerApp gateway interface
/// </summary>
public interface IMessengerAppGateway : IMessageGateway<MessengerAppPayload, MessengerAppResult>, ISchedulableGateway<MessengerAppPayload, MessengerAppScheduleResult>
{
    /// <summary>
    /// Send message with template
    /// </summary>
    Task<MessengerAppResult> SendWithTemplateAsync(string templateId, object templateData, string recipientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk messages with template
    /// </summary>
    Task<IReadOnlyList<MessengerAppResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipientId)> recipients, CancellationToken cancellationToken = default);
}

/// <summary>
/// Push notification gateway interface
/// </summary>
public interface IPushGateway : IMessageGateway<PushPayload, PushResult>, ISchedulableGateway<PushPayload, PushScheduleResult>
{
    /// <summary>
    /// Send push notification with template
    /// </summary>
    Task<PushResult> SendWithTemplateAsync(string templateId, object templateData, string deviceToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send bulk push notifications with template
    /// </summary>
    Task<IReadOnlyList<PushResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string deviceToken)> recipients, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribe device to topic
    /// </summary>
    Task<PushSubscriptionResult> SubscribeToTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unsubscribe device from topic
    /// </summary>
    Task<PushSubscriptionResult> UnsubscribeFromTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default);
}
