using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.SMS;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Services.Gateway;

namespace Libraries.Services.Gateways.Base
{
    public abstract class BaseSmsGateway : BaseGateway, ISmsGateway, IAdminGateway, IMetricsGateway
    {
        protected BaseSmsGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName)
            : base(httpClient, dataService, gatewayName)
        {
        }

        // IGateway interface implementations
        public abstract string ProviderName { get; }
        public virtual bool IsEnabled => true;
        public virtual Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        // ISmsGateway interface implementations
        public virtual Task<SmsResult> SendWithTemplateAsync(string templateId, object templateData, string recipient, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Template sending not implemented for this SMS gateway");
        }

        public virtual Task<IReadOnlyList<SmsResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipient)> recipients, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Bulk template sending not implemented for this SMS gateway");
        }

        public virtual Task<PhoneValidationResult> ValidatePhoneAsync(string phoneNumber, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new PhoneValidationResult
            {
                IsValid = !string.IsNullOrWhiteSpace(phoneNumber),
                PhoneNumber = phoneNumber,
                FormattedNumber = phoneNumber,
                CountryCode = null,
                Carrier = null,
                LineType = null
            });
        }

        // Abstract methods that must be implemented by derived classes
        public abstract Task<SmsResult> SendAsync(SmsPayload payload, CancellationToken cancellationToken = default);
        public abstract Task<IReadOnlyList<SmsResult>> SendBulkAsync(IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken = default);
        public abstract Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
        public abstract Task<SmsScheduleResult> ScheduleMessageAsync(SmsPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
        public abstract Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
        public abstract Task<SmsScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, SmsPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
        public abstract Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);
        public abstract GatewayCapabilities GetCapabilities();

        // Helper methods for creating standardized results
        protected SmsResult CreateSuccessResult(SmsPayload payload, string messageId, string responseContent = null)
        {
            return new SmsResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        protected SmsResult CreateErrorResult(SmsPayload payload, string errorMessage, string messageId = null)
        {
            return new SmsResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        protected SmsScheduleResult CreateSuccessScheduleResult(SmsPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        protected SmsScheduleResult CreateErrorScheduleResult(SmsPayload payload, DateTimeOffset scheduledTime, string errorMessage, string scheduledMessageId = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // Default implementations for IAdminGateway
        public virtual async Task<GatewayTestResult> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var testMessage = CreateTestSmsPayload(_gatewayName);
                
                var testResult = await SendAsync(testMessage, cancellationToken);
                return CreateTestResult(
                    testResult.IsSuccess,
                    testResult.IsSuccess ? $"{_gatewayName} gateway test successful" : $"{_gatewayName} gateway test failed",
                    DateTime.UtcNow - startTime,
                    testResult.MessageId,
                    testResult.ErrorMessage
                );
            }
            catch (Exception ex)
            {
                return CreateTestResult(
                    false,
                    $"{_gatewayName} gateway test failed",
                    DateTime.UtcNow - startTime,
                    null,
                    ex.Message
                );
            }
        }

        public virtual async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> config, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveGatewayConfigurationAsync(_gatewayName, config, cancellationToken);
                var configDict = config?.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value) ?? new Dictionary<string, object>();
                return CreateSuccessConfigurationResult(configDict, _gatewayName);
            }
            catch (Exception ex)
            {
                return CreateErrorConfigurationResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
        {
            var config = await _dataService.GetGatewayConfigurationAsync(_gatewayName, cancellationToken);
            return CreateSuccessConfigurationResult(config, _gatewayName);
        }

        public virtual async Task<GatewayTemplateResult> CreateTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                var templateId = await _dataService.CreateTemplateAsync(_gatewayName, template, cancellationToken);
                template.Id = templateId;
                return CreateSuccessTemplateResult(template, _gatewayName);
            }
            catch (Exception ex)
            {
                return CreateErrorTemplateResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayTemplateResult> UpdateTemplateAsync(string templateId, GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.UpdateTemplateAsync(_gatewayName, templateId, template, cancellationToken);
                template.Id = templateId;
                return CreateSuccessTemplateResult(template, _gatewayName);
            }
            catch (Exception ex)
            {
                return CreateErrorTemplateResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
        {
            try
            {
                var success = await _dataService.DeleteTemplateAsync(_gatewayName, templateId, cancellationToken);
                if (success)
                {
                    return CreateSuccessTemplateResult(null, _gatewayName);
                }
                else
                {
                    return CreateErrorTemplateResult("Template not found", _gatewayName);
                }
            }
            catch (Exception ex)
            {
                return CreateErrorTemplateResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
        {
            var templates = await _dataService.GetTemplatesAsync(_gatewayName, cancellationToken);
            return CreateSuccessTemplatesResult(templates.ToList(), _gatewayName);
        }

        public virtual async Task<GatewayRetryPolicyResult> SetRetryPolicyAsync(GatewayRetryPolicy policy, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveRetryPolicyAsync(_gatewayName, policy, cancellationToken);
                return new GatewayRetryPolicyResult
                {
                    IsSuccess = true,
                    Message = "Retry policy updated successfully"
                };
            }
            catch (Exception ex)
            {
                return new GatewayRetryPolicyResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public virtual async Task<GatewayRetryPolicy> GetRetryPolicyAsync(CancellationToken cancellationToken = default)
        {
            return await _dataService.GetRetryPolicyAsync(_gatewayName, cancellationToken);
        }

        // Default implementations for IMetricsGateway
        public virtual async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetUsageMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayPerformanceMetrics> GetPerformanceMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetPerformanceMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetSuccessRateMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetFailureRateMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetLatencyMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetDeliveryCountMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetAnalyticsDashboardAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
        {
            return await _dataService.GetRealTimeMetricsAsync(_gatewayName, cancellationToken);
        }

        public virtual async Task LogMessageAsync(string messageId, string status, Dictionary<string, object> metadata = null, CancellationToken cancellationToken = default)
        {
            await _dataService.LogMessageAsync(_gatewayName, messageId, status, metadata, cancellationToken);
        }

        public virtual async Task RecordMetricAsync(string metricName, double value, Dictionary<string, string> tags = null, CancellationToken cancellationToken = default)
        {
            await _dataService.RecordMetricAsync(_gatewayName, metricName, value, tags, cancellationToken);
        }

        // Missing IAdminGateway methods
        public virtual async Task<GatewayTemplateResult> SaveTemplateAsync(Models.DTOs.Gateway.GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                var savedTemplate = await _dataService.SaveTemplateResultAsync(_gatewayName, ProviderName, template, cancellationToken);
                return savedTemplate;
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    TemplateId = template?.TemplateId,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
        {
            var result = await _dataService.GetRetryPolicyResultAsync(_gatewayName, ProviderName, cancellationToken);
            return result.RetryConfiguration ?? new GatewayRetryConfiguration();
        }

        public virtual async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration configuration, CancellationToken cancellationToken = default)
        {
            var result = await _dataService.UpdateRetryPolicyResultAsync(_gatewayName, ProviderName, configuration, cancellationToken);
            return new GatewayConfigurationResult
            {
                IsSuccess = result.IsSuccess,
                Message = result.Message,
                ErrorMessage = result.ErrorMessage,
                GatewayName = _gatewayName,
                Provider = ProviderName
            };
        }

        public virtual async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
        {
            return new GatewayTestResult
            {
                IsSuccess = true,
                Message = $"{_gatewayName} gateway test completed successfully",
                ResponseTime = TimeSpan.FromMilliseconds(100),
                TestMessageId = Guid.NewGuid().ToString(),
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "Connection",
                    ["GatewayName"] = _gatewayName,
                    ["Timestamp"] = DateTime.UtcNow
                }
            };
        }
    }
}
