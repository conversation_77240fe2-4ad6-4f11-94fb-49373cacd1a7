using System;
using System.Collections.Generic;

namespace Models.DTOs.Gateway
{
    /// <summary>
    /// Gateway configuration result
    /// </summary>
    public class GatewayConfigResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Configuration data (sanitized - no secrets)
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; } = new();

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// When the configuration was last updated
        /// </summary>
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// Gateway name
        /// </summary>
        public string? GatewayName { get; set; }
    }

    /// <summary>
    /// Gateway configuration result with full details
    /// </summary>
    public class GatewayConfigurationResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Configuration data (sanitized - no secrets)
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; } = new();

        /// <summary>
        /// Available configuration options
        /// </summary>
        public Dictionary<string, object> AvailableOptions { get; set; } = new();

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// When the configuration was last updated
        /// </summary>
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// Gateway name
        /// </summary>
        public string? GatewayName { get; set; }

        /// <summary>
        /// Configuration validation results
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new();
    }

    /// <summary>
    /// Gateway templates result
    /// </summary>
    public class GatewayTemplatesResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// List of available templates
        /// </summary>
        public List<GatewayTemplate> Templates { get; set; } = new();

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Total number of templates
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Gateway name
        /// </summary>
        public string? GatewayName { get; set; }
    }

    /// <summary>
    /// Gateway template result
    /// </summary>
    public class GatewayTemplateResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Template data
        /// </summary>
        public GatewayTemplate? Template { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gateway name
        /// </summary>
        public string? GatewayName { get; set; }
    }

    /// <summary>
    /// Gateway template definition
    /// </summary>
    public class GatewayTemplate
    {
        /// <summary>
        /// Template identifier
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Template name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Template content/body
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Template type (email, sms, push, etc.)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Template variables/placeholders
        /// </summary>
        public List<string> Variables { get; set; } = new();

        /// <summary>
        /// Template metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// When the template was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the template was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Whether the template is active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Gateway retry policy result
    /// </summary>
    public class GatewayRetryPolicyResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Retry policy configuration
        /// </summary>
        public GatewayRetryPolicy? RetryPolicy { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gateway name
        /// </summary>
        public string? GatewayName { get; set; }
    }

    /// <summary>
    /// Gateway retry policy configuration
    /// </summary>
    public class GatewayRetryPolicy
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Initial delay between retries (in seconds)
        /// </summary>
        public int InitialDelaySeconds { get; set; } = 1;

        /// <summary>
        /// Maximum delay between retries (in seconds)
        /// </summary>
        public int MaxDelaySeconds { get; set; } = 60;

        /// <summary>
        /// Backoff multiplier for exponential backoff
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// Whether to use exponential backoff
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary>
        /// HTTP status codes that should trigger a retry
        /// </summary>
        public List<int> RetryableStatusCodes { get; set; } = new() { 429, 500, 502, 503, 504 };

        /// <summary>
        /// Exception types that should trigger a retry
        /// </summary>
        public List<string> RetryableExceptions { get; set; } = new();

        /// <summary>
        /// Whether the retry policy is enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// Gateway retry configuration
    /// </summary>
    public class GatewayRetryConfiguration
    {
        /// <summary>
        /// Retry policy settings
        /// </summary>
        public GatewayRetryPolicy RetryPolicy { get; set; } = new();

        /// <summary>
        /// Circuit breaker settings
        /// </summary>
        public Dictionary<string, object> CircuitBreakerSettings { get; set; } = new();

        /// <summary>
        /// Rate limiting settings
        /// </summary>
        public Dictionary<string, object> RateLimitSettings { get; set; } = new();
    }

    /// <summary>
    /// Gateway performance metrics
    /// </summary>
    public class GatewayPerformanceMetrics
    {
        /// <summary>
        /// Average response time in milliseconds
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 95th percentile response time
        /// </summary>
        public double P95ResponseTime { get; set; }

        /// <summary>
        /// 99th percentile response time
        /// </summary>
        public double P99ResponseTime { get; set; }

        /// <summary>
        /// Success rate percentage
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// Error rate percentage
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// Throughput (requests per second)
        /// </summary>
        public double Throughput { get; set; }

        /// <summary>
        /// Total number of requests
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// Number of successful requests
        /// </summary>
        public long SuccessfulRequests { get; set; }

        /// <summary>
        /// Number of failed requests
        /// </summary>
        public long FailedRequests { get; set; }

        /// <summary>
        /// Time period for these metrics
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End time for metrics period
        /// </summary>
        public DateTime EndTime { get; set; }
    }
}
